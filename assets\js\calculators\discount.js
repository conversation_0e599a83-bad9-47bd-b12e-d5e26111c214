/**
 * Discount Calculator Scripts
 */

document.addEventListener('DOMContentLoaded', function() {
  // Initialize Percentage Discount Calculator if it exists on the page
  const percentageDiscountForm = document.getElementById('percentage-discount-form');
  if (percentageDiscountForm) {
    initPercentageDiscountCalculator();
  }
  
  // Initialize Amount-based Discount Calculator if it exists on the page
  const amountBasedDiscountForm = document.getElementById('amount-based-discount-form');
  if (amountBasedDiscountForm) {
    initAmountBasedDiscountCalculator();
  }
  
  // Initialize Bulk Discount Calculator if it exists on the page
  const bulkDiscountForm = document.getElementById('bulk-discount-form');
  if (bulkDiscountForm) {
    initBulkDiscountCalculator();
  }
});

/**
 * Initialize Percentage Discount Calculator
 */
function initPercentageDiscountCalculator() {
  // Get form elements
  const form = document.getElementById('percentage-discount-form');
  const originalPrice = document.getElementById('original-price');
  const discountPercentage = document.getElementById('discount-percentage');
  const results = document.getElementById('discount-results');
  
  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues('percentage-discount');
  if (savedValues) {
    originalPrice.value = savedValues.originalPrice;
    discountPercentage.value = savedValues.discountPercentage;
  }
  
  // Handle form submission
  form.addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Clear previous errors
    calculatorUtils.clearErrors(form);
    
    // Get input values
    const originalPriceValue = parseFloat(originalPrice.value);
    const discountPercentageValue = parseFloat(discountPercentage.value);
    
    // Validate inputs
    const priceValidation = calculatorUtils.validateNumericInput(
      originalPriceValue, 
      0, 
      Number.MAX_SAFE_INTEGER, 
      'Please enter a valid price greater than 0'
    );
    
    if (!priceValidation.valid) {
      calculatorUtils.showError(originalPrice, priceValidation.message);
      return;
    }
    
    const percentageValidation = calculatorUtils.validateNumericInput(
      discountPercentageValue, 
      0, 
      100, 
      'Please enter a valid percentage between 0 and 100'
    );
    
    if (!percentageValidation.valid) {
      calculatorUtils.showError(discountPercentage, percentageValidation.message);
      return;
    }
    
    // Save values to localStorage
    storageManager.saveCalculatorValues('percentage-discount', {
      originalPrice: originalPriceValue,
      discountPercentage: discountPercentageValue
    });
    
    // Calculate discount
    const discountAmount = originalPriceValue * (discountPercentageValue / 100);
    const finalPrice = originalPriceValue - discountAmount;
    
    // Round values to 2 decimal places
    const roundedDiscountAmount = calculatorUtils.round(discountAmount, 2);
    const roundedFinalPrice = calculatorUtils.round(finalPrice, 2);
    
    // Display results
    document.getElementById('result-original-price').textContent = calculatorUtils.formatCurrency(originalPriceValue);
    document.getElementById('result-discount-percentage').textContent = discountPercentageValue + '%';
    document.getElementById('discount-amount').textContent = calculatorUtils.formatCurrency(roundedDiscountAmount);
    document.getElementById('final-price').textContent = calculatorUtils.formatCurrency(roundedFinalPrice);
    document.getElementById('you-save').textContent = calculatorUtils.formatCurrency(roundedDiscountAmount);
    results.style.display = 'block';
    
    // Save calculation to history
    storageManager.saveCalculationHistory('percentage-discount', {
      originalPrice: originalPriceValue,
      discountPercentage: discountPercentageValue,
      discountAmount: roundedDiscountAmount,
      finalPrice: roundedFinalPrice
    });
    
    // Show in-article ad after calculation
    const inArticleAd = document.getElementById('ad-between-calculator-results');
    if (inArticleAd) {
      inArticleAd.style.display = 'block';
    }
  });
}

/**
 * Initialize Amount-based Discount Calculator
 */
function initAmountBasedDiscountCalculator() {
  // Get form elements
  const form = document.getElementById('amount-based-discount-form');
  const originalPrice = document.getElementById('original-price');
  const discountAmount = document.getElementById('discount-amount');
  const results = document.getElementById('discount-results');
  
  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues('amount-based-discount');
  if (savedValues) {
    originalPrice.value = savedValues.originalPrice;
    discountAmount.value = savedValues.discountAmount;
  }
  
  // Handle form submission
  form.addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Clear previous errors
    calculatorUtils.clearErrors(form);
    
    // Get input values
    const originalPriceValue = parseFloat(originalPrice.value);
    const discountAmountValue = parseFloat(discountAmount.value);
    
    // Validate inputs
    const priceValidation = calculatorUtils.validateNumericInput(
      originalPriceValue, 
      0, 
      Number.MAX_SAFE_INTEGER, 
      'Please enter a valid price greater than 0'
    );
    
    if (!priceValidation.valid) {
      calculatorUtils.showError(originalPrice, priceValidation.message);
      return;
    }
    
    const amountValidation = calculatorUtils.validateNumericInput(
      discountAmountValue, 
      0, 
      originalPriceValue, 
      'Please enter a valid discount amount (not greater than the original price)'
    );
    
    if (!amountValidation.valid) {
      calculatorUtils.showError(discountAmount, amountValidation.message);
      return;
    }
    
    // Save values to localStorage
    storageManager.saveCalculatorValues('amount-based-discount', {
      originalPrice: originalPriceValue,
      discountAmount: discountAmountValue
    });
    
    // Calculate discount percentage and final price
    const discountPercentage = (discountAmountValue / originalPriceValue) * 100;
    const finalPrice = originalPriceValue - discountAmountValue;
    
    // Round values to 2 decimal places
    const roundedDiscountPercentage = calculatorUtils.round(discountPercentage, 2);
    const roundedFinalPrice = calculatorUtils.round(finalPrice, 2);
    
    // Display results
    document.getElementById('result-original-price').textContent = calculatorUtils.formatCurrency(originalPriceValue);
    document.getElementById('result-discount-amount').textContent = calculatorUtils.formatCurrency(discountAmountValue);
    document.getElementById('discount-percentage').textContent = roundedDiscountPercentage + '%';
    document.getElementById('final-price').textContent = calculatorUtils.formatCurrency(roundedFinalPrice);
    document.getElementById('you-save').textContent = calculatorUtils.formatCurrency(discountAmountValue);
    results.style.display = 'block';
    
    // Save calculation to history
    storageManager.saveCalculationHistory('amount-based-discount', {
      originalPrice: originalPriceValue,
      discountAmount: discountAmountValue,
      discountPercentage: roundedDiscountPercentage,
      finalPrice: roundedFinalPrice
    });
    
    // Show in-article ad after calculation
    const inArticleAd = document.getElementById('ad-between-calculator-results');
    if (inArticleAd) {
      inArticleAd.style.display = 'block';
    }
  });
}

/**
 * Initialize Bulk Discount Calculator
 */
function initBulkDiscountCalculator() {
  // Get form elements
  const form = document.getElementById('bulk-discount-form');
  const unitPrice = document.getElementById('unit-price');
  const quantity = document.getElementById('quantity');
  const discountPercentage = document.getElementById('discount-percentage');
  const results = document.getElementById('discount-results');
  
  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues('bulk-discount');
  if (savedValues) {
    unitPrice.value = savedValues.unitPrice;
    quantity.value = savedValues.quantity;
    discountPercentage.value = savedValues.discountPercentage;
  }
  
  // Handle form submission
  form.addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Clear previous errors
    calculatorUtils.clearErrors(form);
    
    // Get input values
    const unitPriceValue = parseFloat(unitPrice.value);
    const quantityValue = parseInt(quantity.value);
    const discountPercentageValue = parseFloat(discountPercentage.value);
    
    // Validate inputs
    const priceValidation = calculatorUtils.validateNumericInput(
      unitPriceValue, 
      0, 
      Number.MAX_SAFE_INTEGER, 
      'Please enter a valid unit price greater than 0'
    );
    
    if (!priceValidation.valid) {
      calculatorUtils.showError(unitPrice, priceValidation.message);
      return;
    }
    
    const quantityValidation = calculatorUtils.validateNumericInput(
      quantityValue, 
      1, 
      Number.MAX_SAFE_INTEGER, 
      'Please enter a valid quantity (minimum 1)'
    );
    
    if (!quantityValidation.valid) {
      calculatorUtils.showError(quantity, quantityValidation.message);
      return;
    }
    
    const percentageValidation = calculatorUtils.validateNumericInput(
      discountPercentageValue, 
      0, 
      100, 
      'Please enter a valid percentage between 0 and 100'
    );
    
    if (!percentageValidation.valid) {
      calculatorUtils.showError(discountPercentage, percentageValidation.message);
      return;
    }
    
    // Save values to localStorage
    storageManager.saveCalculatorValues('bulk-discount', {
      unitPrice: unitPriceValue,
      quantity: quantityValue,
      discountPercentage: discountPercentageValue
    });
    
    // Calculate total price, discount amount, and final price
    const totalPrice = unitPriceValue * quantityValue;
    const discountAmount = totalPrice * (discountPercentageValue / 100);
    const finalPrice = totalPrice - discountAmount;
    const discountedUnitPrice = finalPrice / quantityValue;
    
    // Round values to 2 decimal places
    const roundedTotalPrice = calculatorUtils.round(totalPrice, 2);
    const roundedDiscountAmount = calculatorUtils.round(discountAmount, 2);
    const roundedFinalPrice = calculatorUtils.round(finalPrice, 2);
    const roundedDiscountedUnitPrice = calculatorUtils.round(discountedUnitPrice, 2);
    
    // Display results
    document.getElementById('result-unit-price').textContent = calculatorUtils.formatCurrency(unitPriceValue);
    document.getElementById('result-quantity').textContent = quantityValue;
    document.getElementById('total-price').textContent = calculatorUtils.formatCurrency(roundedTotalPrice);
    document.getElementById('result-discount-percentage').textContent = discountPercentageValue + '%';
    document.getElementById('discount-amount').textContent = calculatorUtils.formatCurrency(roundedDiscountAmount);
    document.getElementById('final-price').textContent = calculatorUtils.formatCurrency(roundedFinalPrice);
    document.getElementById('discounted-unit-price').textContent = calculatorUtils.formatCurrency(roundedDiscountedUnitPrice);
    results.style.display = 'block';
    
    // Save calculation to history
    storageManager.saveCalculationHistory('bulk-discount', {
      unitPrice: unitPriceValue,
      quantity: quantityValue,
      discountPercentage: discountPercentageValue,
      totalPrice: roundedTotalPrice,
      discountAmount: roundedDiscountAmount,
      finalPrice: roundedFinalPrice,
      discountedUnitPrice: roundedDiscountedUnitPrice
    });
    
    // Show in-article ad after calculation
    const inArticleAd = document.getElementById('ad-between-calculator-results');
    if (inArticleAd) {
      inArticleAd.style.display = 'block';
    }
  });
}
