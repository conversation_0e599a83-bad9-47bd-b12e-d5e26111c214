<svg width="400" height="250" viewBox="0 0 400 250" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="taxPlanningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <style>
      .title-text { font-family: 'Poppins', sans-serif; font-weight: 700; font-size: 20px; fill: white; }
      .category-text { font-family: 'Poppins', sans-serif; font-weight: 500; font-size: 12px; fill: white; opacity: 0.9; text-transform: uppercase; letter-spacing: 1px; }
      .brand-text { font-family: 'Poppins', sans-serif; font-weight: 500; font-size: 14px; fill: white; opacity: 0.8; }
    </style>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="250" rx="12" fill="url(#taxPlanningGradient)"/>
  
  <!-- Decorative circles -->
  <circle cx="320" cy="50" r="2" fill="white" opacity="0.3"/>
  <circle cx="360" cy="80" r="1.5" fill="white" opacity="0.2"/>
  <circle cx="340" cy="120" r="1" fill="white" opacity="0.4"/>
  
  <!-- Money/Dollar icon -->
  <g transform="translate(340, 20)" fill="white" opacity="0.3">
    <path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/>
  </g>
  
  <!-- Content -->
  <text x="30" y="50" class="category-text">Tax Planning</text>
  <text x="30" y="80" class="title-text">Tax Planning</text>
  <text x="30" y="105" class="title-text">Strategies 2025</text>
  
  <!-- Brand -->
  <text x="270" y="220" class="brand-text">CalculatorSuites</text>
</svg>
