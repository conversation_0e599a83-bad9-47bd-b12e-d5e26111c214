/* Critical CSS - Only essential styles for above-the-fold content */

/* Base styles */
:root {
  --primary-color: #4a6cf7;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --info-color: #17a2b8;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --body-color: #212529;
  --body-bg: #ffffff;
  --font-family-sans-serif: 'Poppins', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-family-heading: 'Poppins', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* Reset */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Critical: Prevent any list numbering on navigation elements */
nav ul,
nav ol,
nav li,
.nav-menu,
.nav-menu li,
.nav-item,
.dropdown-menu,
.dropdown-menu li,
.breadcrumb,
.breadcrumb li,
.breadcrumb-item {
  list-style: none !important;
  list-style-type: none !important;
}

/* Critical: Force override browser default numbering */
nav ol,
.breadcrumb {
  counter-reset: none !important;
}

nav ol li::marker,
.breadcrumb li::marker,
.breadcrumb-item::marker {
  content: none !important;
  display: none !important;
}

html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

body {
  margin: 0;
  font-family: var(--font-family-sans-serif);
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--body-color);
  background-color: var(--body-bg);
  text-align: left;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-family: var(--font-family-heading);
  font-weight: 600;
  line-height: 1.2;
  color: inherit;
}

h1 {
  font-size: 2.25rem;
}

h2 {
  font-size: 1.8rem;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

/* Layout */
.container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
  max-width: 1140px;
}

.grid {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.grid-col-lg-8 {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}

.grid-col-lg-4 {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}

/* Header */
.site-header {
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  padding: 0.5rem 0;
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: inline-block;
}

.logo img {
  max-height: 40px;
  width: auto;
}

.nav-menu {
  display: flex;
  list-style: none;
  list-style-type: none;
  margin: 0;
  padding: 0;
}

/* Ensure no numbering on navigation elements */
.nav-menu li,
.nav-item {
  list-style: none !important;
  list-style-type: none !important;
}

.nav-menu li::before,
.nav-item::before {
  content: none !important;
}

.nav-item {
  position: relative;
  margin: 0 0.5rem;
}

.nav-link {
  color: var(--body-color);
  text-decoration: none;
  padding: 0.5rem 0.75rem;
  display: block;
  font-weight: 500;
}

.nav-link.active {
  color: var(--primary-color);
}

/* Breadcrumb */
.breadcrumb-container {
  background-color: #f8f9fa;
  padding: 0.75rem 0;
  margin-bottom: 1.5rem;
}

.breadcrumb {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  margin: 0;
  list-style: none;
  list-style-type: none;
}

/* Ensure breadcrumbs don't show numbers */
.breadcrumb li,
.breadcrumb-item {
  list-style: none !important;
  list-style-type: none !important;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.breadcrumb-item+.breadcrumb-item {
  padding-left: 0.5rem;
}

.breadcrumb-item+.breadcrumb-item::before {
  display: inline-block;
  padding-right: 0.5rem;
  color: var(--secondary-color);
  content: "/";
}

.breadcrumb-item a {
  color: var(--primary-color);
  text-decoration: none;
}

.breadcrumb-item.active {
  color: var(--secondary-color);
}

/* Main content */
.main-content {
  padding: 2rem 0;
}

.content-section {
  margin-bottom: 2rem;
}

.intro-text {
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
}

/* Calculator container */
.calculator-container {
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1rem;
}

label {
  display: inline-block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

input,
select {
  display: block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.calculate-btn {
  display: inline-block;
  font-weight: 500;
  color: #fff;
  text-align: center;
  vertical-align: middle;
  user-select: none;
  background-color: var(--primary-color);
  border: 1px solid var(--primary-color);
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  cursor: pointer;
}

/* Media queries for responsive layout */
@media (min-width: 992px) {
  .grid-col-lg-8 {
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }

  .grid-col-lg-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
}