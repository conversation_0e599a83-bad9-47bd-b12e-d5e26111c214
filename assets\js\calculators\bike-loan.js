/**
 * Bike Loan EMI Calculator Scripts
 */

document.addEventListener("DOMContentLoaded", function () {
  // Initialize Bike Loan EMI Calculator if it exists on the page
  const bikeLoanCalculatorForm = document.getElementById(
    "bike-loan-calculator-form",
  );
  if (bikeLoanCalculatorForm) {
    initBikeLoanCalculator();
  }
});

/**
 * Initialize Bike Loan EMI Calculator
 */
function initBikeLoanCalculator() {
  // Get form elements
  const form = document.getElementById("bike-loan-calculator-form");
  const bikePrice = document.getElementById("bike-price");
  const downPayment = document.getElementById("down-payment");
  const interestRate = document.getElementById("interest-rate");
  const loanTenure = document.getElementById("loan-tenure");
  const tenureType = document.getElementById("tenure-type");
  const results = document.getElementById("bike-loan-results");

  // Load saved values from localStorage if available
  const savedValues = storageManager.loadCalculatorValues("bike-loan");
  if (savedValues) {
    bikePrice.value = savedValues.bikePrice;
    downPayment.value = savedValues.downPayment;
    interestRate.value = savedValues.interestRate;
    loanTenure.value = savedValues.loanTenure;
    tenureType.value = savedValues.tenureType;
  } else {
    // Set default values
    bikePrice.value = "150000";
    downPayment.value = "30000";
    interestRate.value = "12";
    loanTenure.value = "3";
    tenureType.value = "year";
  }

  // Handle form submission
  form.addEventListener("submit", function (e) {
    e.preventDefault();

    // Clear previous errors
    calculatorUtils.clearErrors(form);

    // Get input values
    const bikePriceValue = parseFloat(bikePrice.value);
    const downPaymentValue = parseFloat(downPayment.value);
    const interestRateValue = parseFloat(interestRate.value);
    const loanTenureValue = parseInt(loanTenure.value);
    const tenureTypeValue = tenureType.value;

    // Validate inputs
    const bikePriceValidation = calculatorUtils.validateNumericInput(
      bikePriceValue,
      10000,
      Number.MAX_SAFE_INTEGER,
      "Please enter a valid bike price (minimum ₹10,000)",
    );

    if (!bikePriceValidation.valid) {
      calculatorUtils.showError(bikePrice, bikePriceValidation.message);
      return;
    }

    const downPaymentValidation = calculatorUtils.validateNumericInput(
      downPaymentValue,
      0,
      bikePriceValue,
      "Down payment cannot exceed bike price",
    );

    if (!downPaymentValidation.valid) {
      calculatorUtils.showError(downPayment, downPaymentValidation.message);
      return;
    }

    const rateValidation = calculatorUtils.validateNumericInput(
      interestRateValue,
      1,
      50,
      "Please enter a valid interest rate between 1% and 50%",
    );

    if (!rateValidation.valid) {
      calculatorUtils.showError(interestRate, rateValidation.message);
      return;
    }

    const tenureValidation = calculatorUtils.validateNumericInput(
      loanTenureValue,
      1,
      tenureTypeValue === "year" ? 7 : 84,
      `Please enter a valid loan tenure between 1 and ${
        tenureTypeValue === "year" ? "7 years" : "84 months"
      }`,
    );

    if (!tenureValidation.valid) {
      calculatorUtils.showError(loanTenure, tenureValidation.message);
      return;
    }

    // Save values to localStorage
    storageManager.saveCalculatorValues("bike-loan", {
      bikePrice: bikePriceValue,
      downPayment: downPaymentValue,
      interestRate: interestRateValue,
      loanTenure: loanTenureValue,
      tenureType: tenureTypeValue,
    });

    // Calculate loan amount
    const loanAmount = bikePriceValue - downPaymentValue;

    // Calculate EMI
    // Convert tenure to months if in years
    const tenureInMonths =
      tenureTypeValue === "year" ? loanTenureValue * 12 : loanTenureValue;

    // Calculate monthly interest rate (annual rate / 12 / 100)
    const monthlyRate = interestRateValue / (12 * 100);

    // Calculate EMI using formula: EMI = [P × r × (1 + r)^n] ÷ [(1 + r)^n - 1]
    // Where P is principal, r is monthly rate, n is number of months
    const emi =
      (loanAmount * monthlyRate * Math.pow(1 + monthlyRate, tenureInMonths)) /
      (Math.pow(1 + monthlyRate, tenureInMonths) - 1);

    // Calculate total payment and total interest
    const totalPayment = emi * tenureInMonths;
    const totalInterest = totalPayment - loanAmount;

    // Round values to 2 decimal places
    const roundedEMI = calculatorUtils.round(emi, 2);
    const roundedTotalPayment = calculatorUtils.round(totalPayment, 2);
    const roundedTotalInterest = calculatorUtils.round(totalInterest, 2);
    const roundedLoanAmount = calculatorUtils.round(loanAmount, 2);

    // Display results
    document.getElementById("loan-amount").textContent =
      calculatorUtils.formatCurrency(roundedLoanAmount);
    document.getElementById("monthly-emi").textContent =
      calculatorUtils.formatCurrency(roundedEMI);
    document.getElementById("total-interest").textContent =
      calculatorUtils.formatCurrency(roundedTotalInterest);
    document.getElementById("total-payment").textContent =
      calculatorUtils.formatCurrency(roundedTotalPayment);
    results.style.display = "block";

    // Generate and display chart
    generateBikeLoanChart(loanAmount, roundedTotalInterest);

    // Save calculation to history
    storageManager.saveCalculationHistory("bike-loan", {
      bikePrice: bikePriceValue,
      downPayment: downPaymentValue,
      loanAmount: roundedLoanAmount,
      interestRate: interestRateValue,
      loanTenure: loanTenureValue,
      tenureType: tenureTypeValue,
      emi: roundedEMI,
      totalInterest: roundedTotalInterest,
      totalPayment: roundedTotalPayment,
    });
  });
}

/**
 * Generate Bike Loan breakdown chart
 */
function generateBikeLoanChart(principal, totalInterest) {
  const chartContainer = document.getElementById("bike-loan-chart-container");
  if (!chartContainer) return;

  chartContainer.innerHTML = ""; // Clear previous chart

  // Create a simple pie chart using HTML/CSS
  const chartEl = document.createElement("div");
  chartEl.className = "pie-chart";

  const totalAmount = principal + totalInterest;
  const principalPercentage = (principal / totalAmount) * 100;
  const interestPercentage = (totalInterest / totalAmount) * 100;

  // Create chart legend
  const legendEl = document.createElement("div");
  legendEl.className = "chart-legend";

  const principalLegend = document.createElement("div");
  principalLegend.className = "legend-item";
  principalLegend.innerHTML = `<span class="legend-color" style="background-color: var(--primary-color);"></span>
                              <span class="legend-text">Principal: ${calculatorUtils.formatCurrency(
                                principal,
                              )} (${principalPercentage.toFixed(1)}%)</span>`;

  const interestLegend = document.createElement("div");
  interestLegend.className = "legend-item";
  interestLegend.innerHTML = `<span class="legend-color" style="background-color: var(--secondary-color);"></span>
                             <span class="legend-text">Interest: ${calculatorUtils.formatCurrency(
                               totalInterest,
                             )} (${interestPercentage.toFixed(1)}%)</span>`;

  legendEl.appendChild(principalLegend);
  legendEl.appendChild(interestLegend);

  // Create the actual pie chart
  const pieEl = document.createElement("div");
  pieEl.className = "pie";
  pieEl.style.background = `conic-gradient(
    var(--primary-color) 0% ${principalPercentage}%,
    var(--secondary-color) ${principalPercentage}% 100%
  )`;

  chartEl.appendChild(pieEl);
  chartContainer.appendChild(chartEl);
  chartContainer.appendChild(legendEl);
}
