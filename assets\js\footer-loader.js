/**
 * Footer loader utility - ensures footer is present on all pages
 */

// Function to load footer if not already present
function ensureFooterLoaded() {
  // Check if footer already exists
  if (document.querySelector('.site-footer')) {
    console.log('Footer already exists');
    return;
  }

  console.log('Loading footer...');
  
  // Try to fetch footer.html
  fetch('/footer.html')
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.text();
    })
    .then(data => {
      // Insert footer at the end of body
      document.body.insertAdjacentHTML('beforeend', data);
      
      // Ensure footer is visible
      const footer = document.querySelector('.site-footer');
      if (footer) {
        footer.style.opacity = '1';
        footer.style.transition = 'opacity 0.3s ease-in-out';
        console.log('Footer loaded successfully');
      }
    })
    .catch(error => {
      console.error('Error loading footer:', error);
      
      // Fallback: create a basic footer
      const fallbackFooter = `
        <footer class="site-footer">
          <div class="container">
            <div class="footer-grid">
              <div class="footer-column">
                <h4>Calculator Suites</h4>
                <p>Free online calculators for all your financial, tax, health, and discount calculation needs.</p>
              </div>
              <div class="footer-column">
                <h4>Calculator Categories</h4>
                <ul class="footer-links">
                  <li><a href="/tax/">Tax Calculators</a></li>
                  <li><a href="/discount/">Discount Calculators</a></li>
                  <li><a href="/investment/">Investment Calculators</a></li>
                  <li><a href="/loan/">Loan Calculators</a></li>
                  <li><a href="/health/">Health Calculators</a></li>
                </ul>
              </div>
              <div class="footer-column">
                <h4>Popular Calculators</h4>
                <ul class="footer-links">
                  <li><a href="/tax/free-gst-calculator/">GST Calculator</a></li>
                  <li><a href="/investment/sip-calculator/">SIP Calculator</a></li>
                  <li><a href="/loan/emi-calculator/">EMI Calculator</a></li>
                  <li><a href="/health/bmi-calculator/">BMI Calculator</a></li>
                </ul>
              </div>
              <div class="footer-column">
                <h4>Contact</h4>
                <ul class="footer-links">
                  <li><a href="/contact.html">Contact Us</a></li>
                  <li><a href="/privacy.html">Privacy Policy</a></li>
                  <li><a href="/how-it-works.html">How It Works</a></li>
                  <li><a href="/faq.html">FAQ</a></li>
                </ul>
              </div>
            </div>
            <div class="footer-bottom">
              <p>&copy; 2025 CalculatorSuites. All rights reserved.</p>
            </div>
          </div>
        </footer>
      `;
      
      document.body.insertAdjacentHTML('beforeend', fallbackFooter);
      
      // Make fallback footer visible
      const footer = document.querySelector('.site-footer');
      if (footer) {
        footer.style.opacity = '1';
        footer.style.transition = 'opacity 0.3s ease-in-out';
        console.log('Fallback footer loaded');
      }
    });
}

// Load footer when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', ensureFooterLoaded);
} else {
  ensureFooterLoaded();
}
