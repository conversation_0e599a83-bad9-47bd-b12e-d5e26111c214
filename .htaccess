# Enable GZIP compression
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json
</IfModule>

# Set browser caching
<IfModule mod_expires.c>
  ExpiresActive On

  # Images
  ExpiresByType image/jpeg "access plus 1 year"
  ExpiresByType image/png "access plus 1 year"
  ExpiresByType image/webp "access plus 1 year"
  ExpiresByType image/svg+xml "access plus 1 year"
  ExpiresByType image/x-icon "access plus 1 year"

  # CSS, JavaScript
  ExpiresByType text/css "access plus 1 month"
  ExpiresByType text/javascript "access plus 1 month"
  ExpiresByType application/javascript "access plus 1 month"

  # Fonts
  ExpiresByType font/woff "access plus 1 year"
  ExpiresByType font/woff2 "access plus 1 year"
  ExpiresByType application/font-woff "access plus 1 year"
  ExpiresByType application/font-woff2 "access plus 1 year"

  # HTML - no caching to ensure latest version
  ExpiresByType text/html "access plus 0 seconds"
</IfModule>

# Custom error pages
ErrorDocument 404 /404.html

# Prevent directory listing
Options -Indexes

# Redirect www to non-www
RewriteEngine On
RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

# Redirect HTTP to HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Redirect from mortgage directory to loan directory
RewriteRule ^mortgage/(.*)$ /loan/$1 [R=301,L]

# Redirect from mortgage index to loan index
RewriteRule ^mortgage/?$ /loan/ [R=301,L]

# Clean URL rewriting - Remove .html extension and handle trailing slashes
# First, redirect .html URLs to clean URLs
RewriteCond %{THE_REQUEST} /([^.]+)\.html [NC]
RewriteRule ^ /%1/ [NC,L,R=301]

# Handle clean URLs by internally rewriting to .html files
# For calculator pages with trailing slash
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^/]+)/([^/]+)/?$ /$1/$2.html [L]

# For category index pages with trailing slash
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^([^/]+)/?$ /$1/index.html [L]

# Handle blog URLs with trailing slash
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^blog/([^/]+)/?$ /blog/$1.html [L]

# Handle llms.txt specifically
RewriteRule ^llms\.txt$ /llm.txt [L]

# Specific redirects for renamed files
RewriteRule ^tax/free-gst-calculator/?$ /tax/gst-calculator.html [L]
