@echo off
echo Testing Navigation URL Fix...
echo.

echo Tax Calculator Navigation Test:
echo 1. Go to: http://localhost:8000/tax/
echo 2. Click on "Income Tax Calculator" in the navigation menu
echo 3. URL should be: http://localhost:8000/tax/free-income-tax/
echo 4. NOT: http://localhost:8000/tax/tax/free-income-tax/ (duplicate)
echo.

echo Loan Calculator Navigation Test:
echo 1. Go to: http://localhost:8000/loan/
echo 2. Click on "EMI Calculator" in the navigation menu  
echo 3. URL should be: http://localhost:8000/loan/free-emi-calculator/
echo 4. NOT: http://localhost:8000/loan/loan/free-emi-calculator/ (duplicate)
echo.

echo Investment Calculator Navigation Test:
echo 1. Go to: http://localhost:8000/investment/
echo 2. Click on "SIP Calculator" in the navigation menu
echo 3. URL should be: http://localhost:8000/investment/free-sip-calculator/
echo 4. NOT: http://localhost:8000/investment/investment/free-sip-calculator/ (duplicate)
echo.

echo Health Calculator Navigation Test:
echo 1. Go to: http://localhost:8000/health/
echo 2. Click on "BMI Calculator" in the navigation menu
echo 3. URL should be: http://localhost:8000/health/free-bmi-calculator/
echo 4. NOT: http://localhost:8000/health/health/free-bmi-calculator/ (duplicate)
echo.

echo If any URLs show duplicates, the navigation links need to be fixed.
echo.
pause
