const http = require("http");
const fs = require("fs");
const path = require("path");

const PORT = 8000;

const mimeTypes = {
  ".html": "text/html",
  ".css": "text/css",
  ".js": "text/javascript",
  ".json": "application/json",
  ".png": "image/png",
  ".jpg": "image/jpeg",
  ".jpeg": "image/jpeg",
  ".gif": "image/gif",
  ".svg": "image/svg+xml",
  ".ico": "image/x-icon",
  ".webp": "image/webp",
  ".woff": "font/woff",
  ".woff2": "font/woff2",
  ".ttf": "font/ttf",
  ".eot": "application/vnd.ms-fontobject",
};

const server = http.createServer((req, res) => {
  // Clean up URL - remove any escaped slashes
  let cleanRequestUrl = req.url.replace(/\\\//g, "/");
  let filePath = "." + cleanRequestUrl;

  // Handle llms.txt specifically
  if (cleanRequestUrl === "/llms.txt") {
    filePath = "./llm.txt";
  }
  // Handle specific GST calculator redirect
  else if (
    cleanRequestUrl === "/tax/free-gst-calculator/" ||
    cleanRequestUrl === "/tax/free-gst-calculator"
  ) {
    filePath = "./tax/gst-calculator.html";
  }
  // Handle clean URLs for calculator pages (e.g., /discount/free-amount-based/ -> /discount/free-amount-based.html)
  else if (cleanRequestUrl.match(/^\/[^\/]+\/[^\/]+\/?$/)) {
    const cleanUrl = cleanRequestUrl.replace(/\/$/, ""); // Remove trailing slash
    filePath = "." + cleanUrl + ".html";
  }
  // Handle clean URLs for category pages (e.g., /discount/ -> /discount/index.html)
  else if (cleanRequestUrl.match(/^\/[^\/]+\/?$/) && cleanRequestUrl !== "/") {
    const cleanUrl = cleanRequestUrl.replace(/\/$/, ""); // Remove trailing slash
    filePath = "." + cleanUrl + "/index.html";
  }
  // Handle blog URLs (e.g., /blog/some-post/ -> /blog/some-post.html)
  else if (cleanRequestUrl.match(/^\/blog\/[^\/]+\/?$/)) {
    const cleanUrl = cleanRequestUrl.replace(/\/$/, ""); // Remove trailing slash
    filePath = "." + cleanUrl + ".html";
  }
  // Default to index.html if no file specified
  else if (filePath === "./") {
    filePath = "./index.html";
  }
  // Handle directory requests
  else if (filePath.endsWith("/")) {
    filePath += "index.html";
  }

  const extname = path.extname(filePath).toLowerCase();
  const contentType = mimeTypes[extname] || "text/plain";

  fs.readFile(filePath, (err, data) => {
    if (err) {
      if (err.code === "ENOENT") {
        res.writeHead(404, { "Content-Type": "text/html" });
        res.end(
          "<h1>404 Not Found</h1><p>The requested file was not found.</p>",
        );
      } else {
        res.writeHead(500, { "Content-Type": "text/html" });
        res.end(
          "<h1>500 Internal Server Error</h1><p>Something went wrong on the server.</p>",
        );
      }
    } else {
      res.writeHead(200, { "Content-Type": contentType });
      res.end(data);
    }
  });
});

server.listen(PORT, () => {
  console.log(`Local server running at:`);
  console.log(`http://localhost:${PORT}`);
  console.log(`http://127.0.0.1:${PORT}`);
  console.log(`\nTo test the blog index, visit:`);
  console.log(`http://localhost:${PORT}/blog/`);
  console.log(`\nPress Ctrl+C to stop the server`);
});
