@echo off
echo Verifying 404 error fixes...
echo.

echo 1. Checking if favicon.ico exists...
if exist favicon.ico (
    echo ✓ favicon.ico found
) else (
    echo ✗ favicon.ico missing
)
echo.

echo 2. Checking for remaining relative asset paths...
echo Searching for "assets/js/main.js" (should be "/assets/js/main.js")...
findstr /s "src=\"assets/js/main.js\"" *.html >nul 2>&1
if %errorlevel% equ 0 (
    echo ✗ Found files still using relative paths:
    findstr /s "src=\"assets/js/main.js\"" *.html
) else (
    echo ✓ No relative asset paths found
)
echo.

echo 3. Checking if main.js file exists...
if exist assets\js\main.js (
    echo ✓ /assets/js/main.js found
) else (
    echo ✗ /assets/js/main.js missing
)
echo.

echo 4. Testing key calculator URLs (requires server to be running)...
echo Visit these URLs to verify they work:
echo   http://localhost:8000/loan/free-emi-calculator
echo   http://localhost:8000/health/free-bmi-calculator  
echo   http://localhost:8000/investment/free-sip-calculator
echo   http://localhost:8000/tax/gst-calculator
echo   http://localhost:8000/discount/free-percentage
echo.

echo Verification complete!
pause
