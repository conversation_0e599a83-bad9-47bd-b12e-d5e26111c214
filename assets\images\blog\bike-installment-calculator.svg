<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600" width="1200" height="600">
  <defs>
    <linearGradient id="bikeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </linearGradient>
    <pattern id="gridPattern" patternUnits="userSpaceOnUse" width="50" height="50">
      <path d="M 0 0 L 0 50 L 50 50" stroke="#ffffff" stroke-width="0.5" opacity="0.1" fill="none"/>
    </pattern>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="600" fill="url(#bikeGradient)"/>
  <rect width="1200" height="600" fill="url(#gridPattern)"/>
  
  <!-- Motorcycle Icon -->
  <g transform="translate(150, 250)">
    <path d="M100 50 C100 22.386 77.614 0 50 0 S0 22.386 0 50 s22.386 50 50 50 S100 77.614 100 50z" 
          fill="none" stroke="#ffffff" stroke-width="3" opacity="0.3"/>
    <path d="M50 20 C50 20 80 30 80 50 S65 80 50 80 S20 70 20 50 S50 20 50 20z" 
          fill="#ffffff" opacity="0.2"/>
    <circle cx="50" cy="50" r="8" fill="#ffffff" opacity="0.4"/>
    
    <!-- Second wheel -->
    <g transform="translate(200, 0)">
      <path d="M100 50 C100 22.386 77.614 0 50 0 S0 22.386 0 50 s22.386 50 50 50 S100 77.614 100 50z" 
            fill="none" stroke="#ffffff" stroke-width="3" opacity="0.3"/>
      <circle cx="50" cy="50" r="8" fill="#ffffff" opacity="0.4"/>
    </g>
    
    <!-- Bike body -->
    <path d="M50 50 L250 50 L200 20 L100 20 Z" fill="#ffffff" opacity="0.2"/>
    <path d="M150 20 L150 0 L180 0" stroke="#ffffff" stroke-width="3" fill="none" opacity="0.3"/>
  </g>
  
  <!-- Calculator Icon -->
  <g transform="translate(800, 150)">
    <rect x="0" y="0" width="200" height="300" rx="10" fill="none" stroke="#ffffff" stroke-width="3" opacity="0.3"/>
    <rect x="20" y="20" width="160" height="60" rx="5" fill="#ffffff" opacity="0.2"/>
    
    <!-- Calculator buttons -->
    <g opacity="0.3">
      <rect x="20" y="100" width="35" height="35" rx="5" fill="#ffffff"/>
      <rect x="65" y="100" width="35" height="35" rx="5" fill="#ffffff"/>
      <rect x="110" y="100" width="35" height="35" rx="5" fill="#ffffff"/>
      <rect x="155" y="100" width="35" height="35" rx="5" fill="#ffffff"/>
      
      <rect x="20" y="145" width="35" height="35" rx="5" fill="#ffffff"/>
      <rect x="65" y="145" width="35" height="35" rx="5" fill="#ffffff"/>
      <rect x="110" y="145" width="35" height="35" rx="5" fill="#ffffff"/>
      <rect x="155" y="145" width="35" height="35" rx="5" fill="#ffffff"/>
      
      <rect x="20" y="190" width="35" height="35" rx="5" fill="#ffffff"/>
      <rect x="65" y="190" width="35" height="35" rx="5" fill="#ffffff"/>
      <rect x="110" y="190" width="35" height="35" rx="5" fill="#ffffff"/>
      <rect x="155" y="190" width="35" height="35" rx="5" fill="#ffffff"/>
      
      <rect x="20" y="235" width="80" height="35" rx="5" fill="#ffffff"/>
      <rect x="110" y="235" width="80" height="35" rx="5" fill="#ffffff"/>
    </g>
  </g>
  
  <!-- Currency symbols -->
  <text x="500" y="200" font-family="Arial, sans-serif" font-size="60" fill="#ffffff" opacity="0.2">₹</text>
  <text x="650" y="350" font-family="Arial, sans-serif" font-size="40" fill="#ffffff" opacity="0.15">$</text>
  <text x="400" y="400" font-family="Arial, sans-serif" font-size="50" fill="#ffffff" opacity="0.2">€</text>
  
  <!-- Title Text -->
  <text x="600" y="280" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="#ffffff" text-anchor="middle">
    Bike Installment Calculator
  </text>
  <text x="600" y="330" font-family="Arial, sans-serif" font-size="24" fill="#ffffff" opacity="0.9" text-anchor="middle">
    Complete Guide to Two-Wheeler EMI 2024
  </text>
  
  <!-- Decorative elements -->
  <circle cx="100" cy="100" r="30" fill="none" stroke="#ffffff" stroke-width="2" opacity="0.1"/>
  <circle cx="1100" cy="500" r="40" fill="none" stroke="#ffffff" stroke-width="2" opacity="0.1"/>
  <circle cx="1050" cy="100" r="25" fill="none" stroke="#ffffff" stroke-width="2" opacity="0.1"/>
</svg>
